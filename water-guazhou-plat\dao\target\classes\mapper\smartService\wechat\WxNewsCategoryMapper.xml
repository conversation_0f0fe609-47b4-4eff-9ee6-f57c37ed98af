<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxNewsCategoryMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        name,
        serial_no,
        tenant_id
        <!--@sql from wx_news_category -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxNewsCategory">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="serial_no" property="serialNo"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from wx_news_category
        <where>
            and tenant_id = #{tenantId}
        </where>
        order by serial_no
    </select>

    <update id="updateFully">
        update wx_news_category
        set name      = #{name},
            serial_no = #{serialNo}
        where id = #{id}
    </update>
</mapper>