<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.BusinessOutletMapper">
    <sql id="Base_Column_List">
        <!--@sql select-->
        id,
        image,
        name,
        phone,
        lat,
        lon,
        address,
        begin_time,
        end_time,
        remark,
        tenant_id
        <!--@sql from business_outlet -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.BusinessOutlet">
        <result column="id" property="id"/>
        <result column="image" property="image"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="lat" property="lat"/>
        <result column="lon" property="lon"/>
        <result column="address" property="address"/>
        <result column="begin_time" property="beginTime"/>
        <result column="end_time" property="endTime"/>
        <result column="remark" property="remark"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from business_outlet
        <where>
            and tenant_id = #{tenantId}
        </where></select>

    <update id="updateFully">
        update business_outlet
        set image      = #{image},
            name       = #{name},
            phone      = #{phone},
            lat        = #{lat},
            lon        = #{lon},
            address    = #{address},
            begin_time = #{beginTime},
            end_time   = #{endTime},
            remark     = #{remark}
        where id = #{id}
    </update>

</mapper>