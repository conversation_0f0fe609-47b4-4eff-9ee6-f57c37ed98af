/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.rule.engine.action;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.BoundHashOperations;
import org.thingsboard.rule.engine.api.*;
import org.thingsboard.rule.engine.telemetry.TbMsgTimeseriesNodeConfiguration;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.plugin.ComponentType;
import org.thingsboard.server.common.msg.TbMsg;
import org.thingsboard.server.dao.model.ModelConstants;
import org.thingsboard.server.dao.model.sql.StationAttrEntity;
import org.thingsboard.server.dao.model.sql.StationEntity;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmCenter;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRule;
import org.thingsboard.server.dao.model.sql.alarmV2.AlarmRuleSmart;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 根据创建遥测数据的属性来进行站点报警
 */
@Slf4j
@RuleNode(
        type = ComponentType.ACTION,
        name = "站点报警",
        configClazz = TbMsgTimeseriesNodeConfiguration.class,
        nodeDescription = "遥测数据触发告警",
        nodeDetails = "过滤数据设置的警报",
        uiResources = {"static/rulenode/rulenode-core-config.js", "static/rulenode/rulenode-core-config.css"},
        configDirective = "tbActionNodeTimeseriesConfig",
        icon = "notifications_active"
)
public class TbMsgTelemetryAttributeAlarmV2Node implements TbNode {

    @Override
    public void init(TbContext ctx, TbNodeConfiguration configuration) throws TbNodeException {
    }

    /**
     * 获取数据并使用属性条件进行筛选
     *
     * @param ctx
     * @param msg
     */
    @Override
    public void onMsg(TbContext ctx, TbMsg msg) {
        try {
//            filterData2(ctx, msg);
            try {
                // 新报警
//                filterData3(ctx, msg);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ctx.tellNext(msg, TbRelationTypes.SUCCESS);
        }

    }

    private void filterData3(TbContext tbContext, TbMsg tbMsg) {
        try {
            log.info("进入报警规则链");
            // 查询设备信息
            Device device = tbContext.getDeviceService().findDeviceById(new DeviceId(UUIDConverter.fromString(tbMsg.getMetaData().getValue(DataConstants.DEVICE_ID))));
            if (device == null) {
                return;
            }
            // 设备的变量列表
            List<String> attrList = new ArrayList<>();
            Map<String, TsKvEntry> dataMap = new HashMap<>();
            for (TsKvEntry tsKvEntry : tbMsg.getMetaData().getTsKvEntryList()) {
                String key = tsKvEntry.getKey();
                attrList.add(key);

                dataMap.put(key, tsKvEntry);
            }
            // 查询站点属性列表
            List<StationAttrEntity> stationAttrList = tbContext.getStationAttrService().findByDeviceIdAndAttr(device.getUuidId().toString(), attrList);
            if (stationAttrList == null || stationAttrList.isEmpty()) {
                log.info("属性不存在");
                return;
            }
            Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
            for (StationAttrEntity stationAttr : stationAttrList) {
                stationAttrMap.put(stationAttr.getStationId() + stationAttr.getAttr(), stationAttr);
            }
            // 查询报警配置列表
            List<AlarmRuleSmart> alarmRuleList = tbContext.getAlarmRuleSmartService().findByStationAttrList(stationAttrList.stream().map(StationAttrEntity::getStationId).collect(Collectors.toList()), (stationAttrList.stream().map(StationAttrEntity::getAttr).collect(Collectors.toList())));
            if (alarmRuleList == null || alarmRuleList.isEmpty()) {
                return;
            }
            // 查询站点列表
            List<String> stationIdList = alarmRuleList.stream().map(a -> a.getStationId()).collect(Collectors.toList());
            List<StationEntity> stationList = tbContext.getStationService().findByStationIdList(stationIdList);
            Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

            // 遍历告警配置
            for (AlarmRuleSmart alarmRule : alarmRuleList) {
                boolean reBuildFlag = true;
                // 是否重复报警
                AlarmCenter alarmCenter = tbContext.getAlarmCenterService().lastAlarmByAlarmRuleId(alarmRule.getId());
                if (alarmCenter != null && alarmCenter.getAlarmStatus().equals(DataConstants.ALARMV2_ALARM_STATUS.NEW.getValue())) {
                    reBuildFlag = false;
                }
                if (alarmRule.getInterval() != null && reBuildFlag) {
                    if (alarmCenter != null) {
                        if (alarmCenter.getTime().getTime() + alarmRule.getInterval() * 60 * 1000 > System.currentTimeMillis()) {
                            reBuildFlag = false;
                        }
                    }
                }

                if ("0".equals(alarmRule.getEnabled()) && reBuildFlag) {
                    reBuildFlag = false;
                }

                // 是否报警时间
                Calendar calendar = Calendar.getInstance();
                // 当前时间
                int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
                int currentMinute = calendar.get(Calendar.MINUTE);
                // 是否到生效时间
                if (StringUtils.isNotBlank(alarmRule.getEnableStartTime()) && reBuildFlag) {
                    String[] split = alarmRule.getEnableStartTime().split(":");
                    if (currentHour < Integer.valueOf(split[0])) {
                        reBuildFlag = false;
                    }
                    if (currentHour == Integer.valueOf(split[0]) && currentMinute < Integer.valueOf(split[1])) {
                        reBuildFlag = false;
                    }
                }
                if (StringUtils.isNotBlank(alarmRule.getEnableEndTime()) && reBuildFlag) {
                    String[] split = alarmRule.getEnableEndTime().split(":");
                    if (Integer.valueOf(split[0]) != 0 && Integer.valueOf(split[1]) != 0) {
                        if (currentHour > Integer.valueOf(split[0])) {
                            reBuildFlag = false;
                        }
                        if (currentHour == Integer.valueOf(split[0]) && currentMinute > Integer.valueOf(split[1])) {
                            reBuildFlag = false;
                        }
                    }

                }

                // 是否报警周几
                if (StringUtils.isNotBlank(alarmRule.getEnableWeekday()) && reBuildFlag) {
                    String[] split = alarmRule.getEnableWeekday().split(",");
                    int weekday = calendar.get(Calendar.DAY_OF_WEEK);
                    boolean flag = false;
                    for (String s : split) {
                        if (weekday == Integer.valueOf(s)) {
                            flag = true;
                            break;
                        }
                    }
                    if (!flag) {
                        reBuildFlag = false;
                    }
                }
                // 查询最新数据
                TsKvEntry tsKvEntry = dataMap.get(alarmRule.getAttr());
                if (tsKvEntry == null) {
                    continue;
                }

                Double x = alarmRule.getUpLimitValue().doubleValue();
                Double y = alarmRule.getDownLimitValue().doubleValue();

                String valueAsString = tsKvEntry.getValueAsString();
                Double data = Double.valueOf(valueAsString);
                if (data > x || data < y) {
                    if (reBuildFlag) {
                        StationEntity station = stationMap.get(alarmRule.getStationId());
                        createAlarmV3(tbContext, alarmRule, tsKvEntry, station, stationAttrMap.get(station.getId() + alarmRule.getAttr()), "3", device);
                    }
                } else {
                    // 恢复告警
                    tbContext.getAlarmCenterService().restoreAlarm(alarmRule.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void destroy() {
        log.info(this.getClass().getName() + "进行销毁" + System.currentTimeMillis());
    }

    /**
     * Filter all data based on criteria
     *
     * @param tbContext
     * @param tbMsg
     * @return
     */
    private void filterData2(TbContext tbContext, TbMsg tbMsg) {
        try {
            log.info("进入报警规则链");
            // 查询设备信息
            Device device = tbContext.getDeviceService().findDeviceById(new DeviceId(UUIDConverter.fromString(tbMsg.getMetaData().getValue(DataConstants.DEVICE_ID))));
            if (device == null) {
                return;
            }
            // 设备的变量列表
            List<String> attrList = new ArrayList<>();
            Map<String, TsKvEntry> dataMap = new HashMap<>();
            for (TsKvEntry tsKvEntry : tbMsg.getMetaData().getTsKvEntryList()) {
                String key = tsKvEntry.getKey();
                attrList.add(key);

                dataMap.put(key, tsKvEntry);
            }
            // 查询站点属性列表
            List<StationAttrEntity> stationAttrList = tbContext.getStationAttrService().findByDeviceIdAndAttr(device.getUuidId().toString(), attrList);
            if (stationAttrList == null || stationAttrList.isEmpty()) {
                log.info("属性不存在");
                return;
            }
            Map<String, StationAttrEntity> stationAttrMap = new HashMap<>();
            for (StationAttrEntity stationAttr : stationAttrList) {
                stationAttrMap.put(stationAttr.getId(), stationAttr);
            }
            // 查询报警配置列表
            List<AlarmRule> alarmRuleList = tbContext.getAlarmRuleService().findByStationAttrList(stationAttrList.stream().map(StationAttrEntity::getId).collect(Collectors.toList()));
            if (alarmRuleList == null || alarmRuleList.isEmpty()) {
                return;
            }
            // 查询站点列表
            List<String> stationIdList = alarmRuleList.stream().map(AlarmRule::getStationId).collect(Collectors.toList());
            List<StationEntity> stationList = tbContext.getStationService().findByStationIdList(stationIdList);
            Map<String, StationEntity> stationMap = stationList.stream().collect(Collectors.toMap(StationEntity::getId, station -> station));

            // 遍历告警配置
            for (AlarmRule alarmRule : alarmRuleList) {
                // 统计已生成的未处理完成报警列表
                boolean reBuildFlag = true;
                Integer count = tbContext.getAlarmCenterService().countAlarmByAlarmRuleId(alarmRule.getId());
                if (count == 0) {
                    // 生成报警
                } else {
                    // 是否允许重复生成报警
                    if ("2".equals(alarmRule.getReAlarmType()) && count > 0) {// 允许
                        Integer reAlarmValue = alarmRule.getReAlarmValue();
                        String reAlarmUnit = alarmRule.getReAlarmUnit();
                        if (reAlarmValue != null && StringUtils.isNotBlank(reAlarmUnit)) {
                            long reAlarmTime = -1;
                            switch (reAlarmUnit) {
                                case "minute":
                                    reAlarmTime = reAlarmValue * 60 * 1000;
                                    break;
                                case "hour":
                                    reAlarmTime = reAlarmValue * 60 * 60 * 1000;
                                    break;
                                case "day":
                                    reAlarmTime = reAlarmValue * 24 * 60 * 60 * 1000;
                                    break;
                                default:
                                    continue;
                            }
                            // 判断是否达到可生成报警的时间
                            List<AlarmCenter> alarmList = tbContext.getAlarmCenterService().findAlarmByAlarmRuleId(alarmRule.getId());
                            if (alarmList != null && alarmList.size() > 1) {
                                AlarmCenter alarmCenter = alarmList.get(0);
                                if (alarmCenter.getTime().getTime() + reAlarmTime < System.currentTimeMillis()) {// 未达到时间
                                    reBuildFlag = false;
                                }
                            } else {// 数据异常!
                                reBuildFlag = false;
                            }
                        }
                    } else {// 不重复生成报警
                        reBuildFlag = false;
                    }
                }

                // 获取站点变量属性
                String stationAttrId = alarmRule.getStationAttrId();
                StationAttrEntity stationAttr = stationAttrMap.get(stationAttrId);
                if (stationAttr == null) {
                    tbContext.getAlarmCenterService().restoreAlarm(alarmRule.getId());
                    continue;
                }
                // 查询最新数据
                TsKvEntry tsKvEntry = dataMap.get(stationAttr.getAttr());
                if (tsKvEntry == null) {
                    continue;
                }

                // 进入报警逻辑
                StationEntity station = stationMap.get(alarmRule.getStationId());
                buildAlarm(tbContext, alarmRule, tsKvEntry, station, stationAttr, reBuildFlag, device);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 构建报警
     *
     * @param alarmRule   报警规则
     * @param tsKvEntry   数据
     * @param station     站点
     * @param stationAttr 报警变量
     * @param reBuildFlag 是否可重复生成报警
     * @param device      报警设备
     */
    private void buildAlarm(TbContext tbContext, AlarmRule alarmRule, TsKvEntry tsKvEntry, StationEntity station, StationAttrEntity stationAttr, boolean reBuildFlag, Device device) {
        switch (alarmRule.getType()) {
            case "1":// 普通规则
                normalAlarm(tbContext, alarmRule, tsKvEntry, station, stationAttr, reBuildFlag, device);
                break;
            case "2":// 风控规则
                levelAlarm(tbContext, alarmRule, tsKvEntry, station, stationAttr, reBuildFlag, device);
                break;
        }

    }

    /**
     * 风控报警
     *
     * @param tbContext   tb上下文
     * @param alarmRule   报警规则
     * @param tsKvEntry   数据
     * @param station     站点
     * @param stationAttr 报警变量
     * @param reBuildFlag 是否重复报警
     * @param device      报警的设备
     */
    private void levelAlarm(TbContext tbContext, AlarmRule alarmRule, TsKvEntry tsKvEntry, StationEntity station, StationAttrEntity stationAttr, boolean reBuildFlag, Device device) {
        JSONObject ruleParmaObj = JSON.parseObject(alarmRule.getRuleParam());
        String ruleType = alarmRule.getRuleType();
        String alarmScriptX = "";
        String alarmScriptY = "";
        String alarmScriptM = "";
        Double x = ruleParmaObj.getDouble("x");
        Double y = ruleParmaObj.getDouble("y");
        Double m = ruleParmaObj.getDouble("m");

        String valueAsString = tsKvEntry.getValueAsString();
        Double data = Double.valueOf(valueAsString);
//        boolean checkTime = false;
        switch (ruleType) {
            case "3":// 数值等于X
                alarmScriptX = data + "==" + (x == null ? "undefined" : x);
                alarmScriptY = data + "==" + (y == null ? "undefined" : y);
                alarmScriptM = data + "==" + (m == null ? "undefined" : m);
                break;
            case "4":// 数值不等于X
                alarmScriptX = data + "!=" + (x == null ? "undefined" : x);
                alarmScriptY = data + "!=" + (y == null ? "undefined" : y);
                alarmScriptM = data + "!=" + (m == null ? "undefined" : m);
                break;
            case "1":// 数值高于X
                alarmScriptX = data + ">" + (x == null ? "undefined" : x);
                alarmScriptY = data + ">" + (y == null ? "undefined" : y);
                alarmScriptM = data + ">" + (m == null ? "undefined" : m);
                break;
            case "2":// 数值低于X
                alarmScriptX = data + "<" + (x == null ? "undefined" : x);
                alarmScriptY = data + "<" + (y == null ? "undefined" : y);
                alarmScriptM = data + "<" + (m == null ? "undefined" : m);
                break;
            default:
                return;
        }
        // 是否生成告警
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine scriptEngine = manager.getEngineByName("js");
        boolean alarmFlag = false;
        String level = "";
        try {
            if ((boolean) scriptEngine.eval(alarmScriptM)) {// 高风险
                alarmFlag = true;
                level = "high";
            } else if ((boolean) scriptEngine.eval(alarmScriptY)) {// 中风险
                alarmFlag = true;
                level = "middle";
            } else if ((boolean) scriptEngine.eval(alarmScriptX)) {// 低风险
                alarmFlag = true;
                level = "low";
            }
        } catch (ScriptException e) {
            log.error("报警判断执行异常, ", e);
        }
        if (alarmFlag) {
            if (reBuildFlag) {// 可生成报警
                // 产生告警
                createAlarm(tbContext, alarmRule, tsKvEntry, station, stationAttr, level, device);
            }
        } else {
            // 恢复告警
            tbContext.getAlarmCenterService().restoreAlarm(alarmRule.getId());
        }


    }

    /**
     * 普通报警
     *
     * @param tbContext   tb上下文
     * @param alarmRule   报警规则
     * @param tsKvEntry   数据
     * @param station     站点
     * @param stationAttr 报警变量
     * @param reBuildFlag
     * @param device
     */
    private void normalAlarm(TbContext tbContext, AlarmRule alarmRule, TsKvEntry tsKvEntry, StationEntity station, StationAttrEntity stationAttr, boolean reBuildFlag, Device device) {
        JSONObject ruleParmaObj = JSON.parseObject(alarmRule.getRuleParam());
        String ruleType = alarmRule.getRuleType();
        String alarmScript = "";
        Double x = ruleParmaObj.getDouble("x");
        Double y = ruleParmaObj.getDouble("y");
        Double m = ruleParmaObj.getDouble("m");

        String valueAsString = tsKvEntry.getValueAsString();
        Double data = Double.valueOf(valueAsString);
        boolean checkTime = false;
        switch (ruleType) {
            case "1":// 数值等于X
            case "7":// 数值超过M分钟等于X
                alarmScript = data + "==" + x;
                if (m != null && "7".equals(ruleType)) {
                    checkTime = true;
                }
                break;
            case "2":// 数值不等于X
            case "8":// 数值超过M分钟不等于X
                alarmScript = data + "!=" + x;
                if (m != null && "8".equals(ruleType)) {
                    checkTime = true;
                }
                break;
            case "3":// 数值高于X
            case "9":// 数值超过M分钟高于X
                alarmScript = data + ">" + x;
                if (m != null && "9".equals(ruleType)) {
                    checkTime = true;
                }
                break;
            case "4":// 数值低于X
            case "10":// 数值超过M分钟低于X
                alarmScript = data + "<" + x;
                if (m != null && "10".equals(ruleType)) {
                    checkTime = true;
                }
                break;
            case "5":// 数值在X和Y之间
            case "11":// 数值在X和Y之间超过M分钟
                alarmScript = data + ">" + x + "&&" + data + "<" + y;
                if (m != null && "11".equals(ruleType)) {
                    checkTime = true;
                }
                break;
            case "6":// 数值不在X和Y之间
            case "12":// 数值不在X和Y之间超过M分钟
                alarmScript = "!(" + data + ">" + x + "&&" + data + "<" + y + ")";
                if (m != null && "12".equals(ruleType)) {
                    checkTime = true;
                }
                break;
            default:
                return;
        }
        // 是否生成报警
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine scriptEngine = manager.getEngineByName("js");
        boolean alarmFlag = false;
        try {
            alarmFlag = (boolean) scriptEngine.eval(alarmScript);
        } catch (ScriptException e) {
            log.error("报警判断执行异常, 报警语句: [{}]", alarmScript);
        }
        // 校验是否要判断时间
        boolean timeFlag = false;
        if (checkTime) {
            BoundHashOperations<String, Object, Object> hashOps = tbContext.getStringRedisTemplate().boundHashOps("alarm:firstTime");
            String timestampString = (String) hashOps.get(alarmRule.getId());
            long timestamp = System.currentTimeMillis();
            if (StringUtils.isNotBlank(timestampString)) {
                timestamp = Long.parseLong(timestampString);
            }

            long max = Long.parseLong(m * 60 * 1000 + "");
            if (System.currentTimeMillis() > timestamp + max) {
                timeFlag = true;
            } else {
                // 清除计时器
                hashOps.delete(alarmRule.getId());
            }
        }
        if (alarmFlag && (!checkTime || timeFlag)) {
            if (reBuildFlag) {
                // 产生告警
                createAlarm(tbContext, alarmRule, tsKvEntry, station, stationAttr, null, device);
            }
        } else {
            // 恢复告警
            tbContext.getAlarmCenterService().restoreAlarm(alarmRule.getId());
        }

    }

    /**
     * 生成告警
     *
     * @param alarmRule   告警规则
     * @param tsKvEntry   数据
     * @param station     所属站点
     * @param stationAttr 报警变量
     * @param level       风险告警等级
     * @param device      报警的设备
     */
    private void createAlarm(TbContext tbContext, AlarmRule alarmRule, TsKvEntry tsKvEntry, StationEntity station, StationAttrEntity stationAttr, String level, Device device) {
        Date now = new Date();
        AlarmCenter alarmCenter = new AlarmCenter();
        alarmCenter.setStationId(alarmRule.getStationId());
        alarmCenter.setTitle(alarmRule.getTitle());
        alarmCenter.setTime(now);
        alarmCenter.setAlarmType(alarmRule.getAlarmType());
        alarmCenter.setAlarmLevel(alarmRule.getAlarmLevel());
        alarmCenter.setDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));

        String ruleType = alarmRule.getRuleType();
        JSONObject ruleParmaObj = JSON.parseObject(alarmRule.getRuleParam());

        String title = "";
        if (station != null) {
            title = station.getName() + "的[" + device.getName() + "]设备" + stationAttr.getName();
        } else {
            title = alarmCenter.getTitle();
        }
        String alarmInfo = "";
        if ("1".equals(alarmRule.getType())) {
            alarmInfo = getNormalAlarmInfo(tsKvEntry, ruleType, ruleParmaObj, title);
            alarmCenter.setProcessMethod(alarmRule.getProcessMethod());
        }
        if ("2".equals(alarmRule.getType())) {
            alarmInfo = getLevelAlarmInfo(tsKvEntry, ruleType, ruleParmaObj, title, level);
            if ("high".equals(level)) {
                alarmCenter.setProcessMethod(ruleParmaObj.getString("mProcessMethod"));
                alarmCenter.setAlarmLevel("3");
            }
            if ("middle".equals(level)) {
                alarmCenter.setProcessMethod(ruleParmaObj.getString("yProcessMethod"));
                alarmCenter.setAlarmLevel("2");
            }
            if ("low".equals(level)) {
                alarmCenter.setProcessMethod(ruleParmaObj.getString("xProcessMethod"));
                alarmCenter.setAlarmLevel("1");
            }
        }
        alarmCenter.setAlarmInfo(alarmInfo);
        alarmCenter.setAlarmStatus(DataConstants.ALARMV2_ALARM_STATUS.NEW.getValue());// 报警中
        alarmCenter.setProcessStatus(DataConstants.ALARMV2_PROCESS_STATUS.NEW.getValue());// 未处理
        alarmCenter.setTenantId(alarmRule.getTenantId());
        alarmCenter.setAlarmRuleId(alarmRule.getId());

        // 保存数据
        tbContext.getAlarmCenterService().save(alarmCenter);

        // 发送告警通知
        String sendWay = alarmRule.getSendWay();
        if (StringUtils.isNotBlank(sendWay)) {
            JSONObject sendWayObj = JSONObject.parseObject(sendWay);
            List<User> userList = tbContext.getUserService().findUserByTenant(new TenantId(UUIDConverter.fromString(alarmCenter.getTenantId())));
            for (User user : userList) {
                if (sendWayObj.containsKey("app") && sendWayObj.getBooleanValue("app")) {
                    try {
                        tbContext.getSystemNotifyService().sendNotify(
                                DataConstants.SYSTEM_NOTIFY_TYPE.ALARM.getValue(),
                                DataConstants.SYSTEM_NOTIFY_TOPIC.ALARM_CENTER,
                                "系统告警",
                                UUIDConverter.fromTimeUUID(user.getUuidId()),
                                alarmRule.getTenantId()
                        );
                    } catch (Exception e) {
                        log.error("[报警通知-APP]发送APP通知失败, 报警规则名称: {}", alarmRule.getTitle());
                    }
                }
                if (sendWayObj.containsKey("sms") && sendWayObj.getBooleanValue("sms")) {
                    try {
                        String msgVersion = tbContext.getMsgVersion();
                        if ("jinzhou".equals(msgVersion)) {
                            try {
                                JSONObject info = new JSONObject();
                                if (user.getAdditionalInfo() != null) {
                                    info = JSONObject.parseObject(user.getAdditionalInfo().toString());
                                }
                                if (info != null && info.get(ModelConstants.ALARM_FORM_SMS) != null && info.get(ModelConstants.ALARM_FORM_SMS).equals(ModelConstants.ALARM_RELEASE_NOT)) {
                                    tbContext.getJinzhouMsgSendService().sendMsg(user.getPhone(), alarmCenter.getAlarmInfo());
                                }
                            } catch (Exception e) {
                                e.getLocalizedMessage();
                            }
                        }
                    } catch (Exception e) {
                        log.error("[报警通知-SMS]发送短信通知失败, 报警规则名称: {}", alarmRule.getTitle());
                    }
                }
            }
        }
    }

    private void createAlarmV3(TbContext tbContext, AlarmRuleSmart alarmRule, TsKvEntry tsKvEntry, StationEntity station, StationAttrEntity stationAttr, String level, Device device) {
        Date now = new Date();
        AlarmCenter alarmCenter = new AlarmCenter();
        alarmCenter.setStationId(alarmRule.getStationId());
        alarmCenter.setTitle(alarmRule.getStationName());
        alarmCenter.setTime(now);
        alarmCenter.setAlarmType(alarmRule.getWarnType());
        // if (stationAttr.getAttr().contains("flow")) {
        //     alarmCenter.setAlarmType("5");
        // }
        // if (stationAttr.getAttr().contains("pressure")) {
        //     alarmCenter.setAlarmType("10");
        // }
        alarmCenter.setAlarmLevel("3");
        alarmCenter.setDeviceId(UUIDConverter.fromTimeUUID(device.getUuidId()));


        String title = "";
        if (station != null) {
            title = station.getName() + "的[" + device.getName() + "]设备" + stationAttr.getName();
        } else {
            title = alarmCenter.getTitle();
        }
        String alarmInfo = "";
        if ("1".equals(alarmRule.getType())) {
            alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警不在" + alarmRule.getUpLimitValue() + "与" + alarmRule.getDownLimitValue() + "之间";
            ;
            alarmCenter.setProcessMethod("请及时处理");
        }
        alarmCenter.setAlarmInfo(alarmInfo);
        alarmCenter.setAlarmStatus(DataConstants.ALARMV2_ALARM_STATUS.NEW.getValue());// 报警中
        alarmCenter.setProcessStatus(DataConstants.ALARMV2_PROCESS_STATUS.NEW.getValue());// 未处理
        alarmCenter.setTenantId(alarmRule.getTenantId());
        alarmCenter.setAlarmRuleId(alarmRule.getId());
        alarmCenter.setDeviceId(alarmRule.getDeviceId());

        // 保存数据
        tbContext.getAlarmCenterService().save(alarmCenter);

    }

    /**
     * 获取风控报警描述
     *
     * @param tsKvEntry    数据
     * @param ruleType     触发类型
     * @param ruleParmaObj 报警参数
     * @param title        报警标题
     * @param level        报警等级
     * @return 报警描述
     */
    private String getLevelAlarmInfo(TsKvEntry tsKvEntry, String ruleType, JSONObject ruleParmaObj, String title, String level) {
        String x = "";
        if ("high".equals(level)) {
            x = ruleParmaObj.getDouble("m").toString();
        }
        if ("middle".equals(level)) {
            x = ruleParmaObj.getDouble("y").toString();
        }
        if ("low".equals(level)) {
            x = ruleParmaObj.getDouble("x").toString();
        }

        String alarmInfo = "";
        switch (ruleType) {
            case "1":// 数值等于X
                alarmInfo = title + "采集值为" + tsKvEntry.getValueAsString() + ", 触发报警等于" + x;
                break;
            case "2":// 数值不等于X
                alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警不等于" + x;
                break;
            case "3":// 数值高于X
                alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警大于" + x;
                break;
            case "4":// 数值低于X
                alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警小于" + x;
                break;
            default:
                return null;
        }
        return alarmInfo;
    }

    /**
     * 获取普通报警描述
     *
     * @param tsKvEntry    数据
     * @param ruleType     触发类型
     * @param ruleParmaObj 报警参数
     * @param title        报警标题
     * @return 报警描述
     */
    private String getNormalAlarmInfo(TsKvEntry tsKvEntry, String ruleType, JSONObject ruleParmaObj, String title) {
        Double x = ruleParmaObj.getDouble("x");
        Double y = ruleParmaObj.getDouble("y");
        Integer m = ruleParmaObj.getInteger("m");

        String alarmInfo = "";
        switch (ruleType) {
            case "1":// 数值等于X
            case "7":// 数值超过M分钟等于X
                alarmInfo = title + "采集值为" + tsKvEntry.getValueAsString() + ", 触发报警等于" + x;
                if ("7".equals(ruleType)) {
                    alarmInfo = alarmInfo + "且持续时间超过" + m + "分钟";
                }
                break;
            case "2":// 数值不等于X
            case "8":// 数值超过M分钟不等于X
                alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警不等于" + x;
                if ("8".equals(ruleType)) {
                    alarmInfo = alarmInfo + "且持续时间超过" + m + "分钟";
                }
                break;
            case "3":// 数值高于X
            case "9":// 数值超过M分钟高于X
                alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警大于" + x;
                if ("9".equals(ruleType)) {
                    alarmInfo = alarmInfo + "且持续时间超过" + m + "分钟";
                }
                break;
            case "4":// 数值低于X
            case "10":// 数值超过M分钟低于X
                alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警小于" + x;
                if ("10".equals(ruleType)) {
                    alarmInfo = alarmInfo + "且持续时间超过" + m + "分钟";
                }
                break;
            case "5":// 数值在X和Y之间
            case "11":// 数值在X和Y之间超过M分钟
                alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警在" + x + "与" + y + "之间";
                if ("11".equals(ruleType)) {
                    alarmInfo = alarmInfo + "且持续时间超过" + m + "分钟";
                }
                break;
            case "6":// 数值不在X和Y之间
            case "12":// 数值不在X和Y之间超过M分钟
                alarmInfo = title + " 采集值为" + tsKvEntry.getValueAsString() + ", 触发报警不在" + x + "与" + y + "之间";
                if ("12".equals(ruleType)) {
                    alarmInfo = alarmInfo + "且持续时间超过" + m + "分钟";
                }
                break;
            default:
                return null;
        }
        return alarmInfo;
    }


}