<?xml version="1.0" encoding="UTF-8" ?>

<configuration>
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{ISO8601} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="org.thingsboard.server.dao" level="WARN"/>
    <logger name="org.apache.cassandra" level="WARN"/>
    <logger name="org.cassandraunit" level="WARN" />
    <logger name="org.apache.cassandra" level="WARN" />

    <root level="WARN">
        <appender-ref ref="console"/>
    </root>

</configuration>
