#!/bin/sh

if ! getent group tb-http-transport >/dev/null; then
    addgroup --system tb-http-transport
fi

if ! getent passwd tb-http-transport >/dev/null; then
    adduser --quiet \
            --system \
            --ingroup tb-http-transport \
            --quiet \
            --disabled-login \
            --disabled-password \
            --home /usr/share/tb-http-transport \
            --no-create-home \
            -gecos "Thingsboard application" \
            tb-http-transport
fi
