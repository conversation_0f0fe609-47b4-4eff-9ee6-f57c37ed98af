<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.remoteControl.RemoteControlDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.remoteControl.RemoteControlDeviceSetting">
        <id column="id" property="id" />
        <result column="device_id" property="deviceId" />
        <result column="water_plant_id" property="waterPlantId" />
        <result column="water_plant_name" property="waterPlantName" />
        <result column="device_type" property="deviceType" />
        <result column="location" property="location" />
        <result column="install_date" property="installDate" />
        <result column="manufacturer" property="manufacturer" />
        <result column="model" property="model" />
        <result column="description" property="description" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, device_id, water_plant_id, water_plant_name, device_type, location, 
        install_date, manufacturer, model, description, tenant_id, create_time, update_time
    </sql>

    <!-- 分页查询远程控制设备 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM remote_control_device_setting
        WHERE tenant_id = #{tenantId}
        <if test="waterPlantId != null and waterPlantId != ''">
            AND water_plant_id = #{waterPlantId}
        </if>
        <if test="deviceType != null and deviceType != ''">
            AND device_type = #{deviceType}
        </if>
        <if test="location != null and location != ''">
            AND location LIKE CONCAT('%', #{location}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据水厂ID查询设备列表 -->
    <select id="selectByWaterPlantId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM remote_control_device_setting
        WHERE water_plant_id = #{waterPlantId} AND tenant_id = #{tenantId}
        ORDER BY device_type, create_time DESC
    </select>

    <!-- 根据设备类型查询设备列表 -->
    <select id="selectByDeviceType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM remote_control_device_setting
        WHERE device_type = #{deviceType} AND tenant_id = #{tenantId}
        ORDER BY create_time DESC
    </select>

    <!-- 统计设备类型数量 -->
    <select id="countByDeviceType" resultType="java.util.Map">
        SELECT 
            device_type as deviceType,
            COUNT(1) as count
        FROM remote_control_device_setting
        WHERE tenant_id = #{tenantId}
        GROUP BY device_type
        ORDER BY count DESC
    </select>

    <!-- 根据设备ID查询扩展信息 -->
    <select id="selectByDeviceId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM remote_control_device_setting
        WHERE device_id = #{deviceId} AND tenant_id = #{tenantId}
        LIMIT 1
    </select>

</mapper>
