cassandra.cluster_name=Thingsboard Cluster

cassandra.keyspace_name=thingsboard

cassandra.url=127.0.0.1:9142

cassandra.ssl=false

cassandra.jmx=false

cassandra.metrics=true

cassandra.compression=none

cassandra.init_timeout_ms=60000

cassandra.init_retry_interval_ms=3000

cassandra.credentials=false

cassandra.username=

cassandra.password=

cassandra.socket.connect_timeout=5000

cassandra.socket.read_timeout=12000

cassandra.socket.keep_alive=true

cassandra.socket.reuse_address=true

cassandra.socket.so_linger=

cassandra.socket.tcp_no_delay=false

cassandra.socket.receive_buffer_size=

cassandra.socket.send_buffer_size=

cassandra.query.read_consistency_level=ONE

cassandra.query.write_consistency_level=ONE

cassandra.query.default_fetch_size=2000

cassandra.query.ts_key_value_partitioning=HOURS

cassandra.query.ts_key_value_ttl=0

cassandra.query.max_limit_per_request=1000
cassandra.query.buffer_size=100000
cassandra.query.concurrent_limit=1000
cassandra.query.permit_max_wait_time=20000
cassandra.query.rate_limit_print_interval_ms=30000
cassandra.query.tenant_rate_limits.enabled=false
cassandra.query.tenant_rate_limits.configuration=5000:1,100000:60
cassandra.query.tenant_rate_limits.print_tenant_names=false

