<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.deviceIcon.DeviceIconMapper">
    
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, device_type, icon_url, device_status, status_color, creator, create_time, update_time, tenant_id
    </sql>
    
    <!-- 分页查询设备图标 -->
    <select id="findByPage" resultType="org.thingsboard.server.dao.model.sql.deviceIcon.DeviceIcon">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_device_icon
        <where>
            <if test="tenantId != null and tenantId != ''">
                AND tenant_id = #{tenantId}
            </if>
            <if test="deviceType != null and deviceType != ''">
                AND device_type = #{deviceType}
            </if>
            <if test="deviceStatus != null and deviceStatus != ''">
                AND device_status = #{deviceStatus}
            </if>
            <if test="fromTime != null">
                AND create_time &gt;= #{fromTime}
            </if>
            <if test="toTime != null">
                AND create_time &lt;= #{toTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据设备类型查询设备图标 -->
    <select id="findByDeviceType" resultType="org.thingsboard.server.dao.model.sql.deviceIcon.DeviceIcon">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_device_icon
        WHERE device_type = #{deviceType}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据设备类型和设备状态查询设备图标 -->
    <select id="findByDeviceTypeAndStatus" resultType="org.thingsboard.server.dao.model.sql.deviceIcon.DeviceIcon">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_device_icon
        WHERE device_type = #{deviceType}
        AND device_status = #{deviceStatus}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
        LIMIT 1
    </select>
    
    <!-- 批量插入设备图标 -->
    <insert id="batchInsert">
        INSERT INTO tb_device_icon (
            id, device_type, icon_url, device_status, status_color, creator, create_time, tenant_id
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.deviceType}, #{item.iconUrl}, #{item.deviceStatus}, 
                #{item.statusColor}, #{item.creator}, #{item.createTime}, #{item.tenantId}
            )
        </foreach>
    </insert>
    
    <!-- 根据设备类型删除设备图标 -->
    <delete id="deleteByDeviceType">
        DELETE FROM tb_device_icon
        WHERE device_type = #{deviceType}
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id = #{tenantId}
        </if>
    </delete>
    
</mapper>
