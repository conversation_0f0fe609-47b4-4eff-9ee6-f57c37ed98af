<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e48bac5b-c61c-486e-9075-928ddfd1d330" name="Changes" comment="切换influxdb">
      <change beforePath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/action/TbMsgTelemetryAttributeAlarmV2Node.java" beforeDir="false" afterPath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/action/TbMsgTelemetryAttributeAlarmV2Node.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/telemetry/TbMsgInfluxDBNode.java" beforeDir="false" afterPath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/telemetry/TbMsgInfluxDBNode.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/telemetry/TbMsgTelemetryAttributeFilterNode.java" beforeDir="false" afterPath="$PROJECT_DIR$/rule-engine/rule-engine-components/src/main/java/org/thingsboard/rule/engine/telemetry/TbMsgTelemetryAttributeFilterNode.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="module" />
    </option>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="guazhou" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\apache-maven-3.6.3" />
        <option name="localRepository" value="D:\apache-maven-3.6.3\.m2" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\apache-maven-3.6.3\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zf1nuPmPiDgf9h6YodyiJ3iOi7" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.ThingsboardServerApplication.executor": "Run",
    "Maven.thingsboard [clean].executor": "Run",
    "Maven.thingsboard [install].executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/Code-Yanfayun/water/guazhou/water-guazhou-plat",
    "project.structure.last.edited": "Project",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "preferences.pluginManager"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CreateClassDialog.RecentsKey">
      <recent name="org.thingsboard.server.dao.sql.workOrder" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\Code-Yanfayun\water\guazhou\water-guazhou-plat" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="ThingsboardServerApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="org.thingsboard.server.ThingsboardServerApplication" />
      <module name="application" />
      <shortenClasspath name="CLASSPATH_FILE" />
      <option name="VM_PARAMETERS" value="-Dspring.output.ansi.enabled=ALWAYS" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="org.thingsboard.server.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="water-guazhou-plat" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="water-guazhou-plat" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.ThingsboardServerApplication" />
        <item itemvalue="Application.ThingsboardServerApplication" />
        <item itemvalue="Application.ThingsboardServerApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e48bac5b-c61c-486e-9075-928ddfd1d330" name="Changes" comment="" />
      <created>1752107486482</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752107486482</updated>
    </task>
    <task id="LOCAL-00001" summary="维修总览功能相关代码新增">
      <option name="closed" value="true" />
      <created>1752819162191</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752819162191</updated>
    </task>
    <task id="LOCAL-00002" summary="事件上报功能">
      <option name="closed" value="true" />
      <created>1753088193597</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1753088193597</updated>
    </task>
    <task id="LOCAL-00003" summary="维修总览和事件总览查询时间bug修复">
      <option name="closed" value="true" />
      <created>1753251522862</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1753251522862</updated>
    </task>
    <task id="LOCAL-00004" summary="设备权限功能修改，设备来源于实际物联网设备">
      <option name="closed" value="true" />
      <created>1753408076216</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1753408076216</updated>
    </task>
    <task id="LOCAL-00005" summary="用户新增控制密钥字段，增加相关功能，同时新增用户时自动添加密钥">
      <option name="closed" value="true" />
      <created>1753769000728</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1753769000728</updated>
    </task>
    <task id="LOCAL-00006" summary="生产控制，设备远程控制功能">
      <option name="closed" value="true" />
      <created>1753769200056</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1753769200056</updated>
    </task>
    <task id="LOCAL-00007" summary="删除设备等内容接口">
      <option name="closed" value="true" />
      <created>1753781122212</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1753781122212</updated>
    </task>
    <task id="LOCAL-00008" summary="修改包名">
      <option name="closed" value="true" />
      <created>1753873783613</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1753873783613</updated>
    </task>
    <task id="LOCAL-00009" summary="修改pom去除gradle内容，新增dockerfile">
      <option name="closed" value="true" />
      <created>1753873944635</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1753873944635</updated>
    </task>
    <task id="LOCAL-00010" summary="增加Californium文件">
      <option name="closed" value="true" />
      <created>1753874290040</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1753874290040</updated>
    </task>
    <task id="LOCAL-00011" summary="增加Californium文件">
      <option name="closed" value="true" />
      <created>1753874299909</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1753874299909</updated>
    </task>
    <task id="LOCAL-00012" summary="修改部分文件报名">
      <option name="closed" value="true" />
      <created>1753926389482</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1753926389482</updated>
    </task>
    <task id="LOCAL-00013" summary="修改netty-mqtt pom">
      <option name="closed" value="true" />
      <created>1753928653468</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1753928653468</updated>
    </task>
    <task id="LOCAL-00014" summary="修改maven-compiler-plugin 相关内容">
      <option name="closed" value="true" />
      <created>1753929996340</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1753929996340</updated>
    </task>
    <task id="LOCAL-00015" summary="修改pom不使用公共仓库">
      <option name="closed" value="true" />
      <created>1753932486183</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1753932486183</updated>
    </task>
    <task id="LOCAL-00016" summary="修改pom不使用公共仓库和gradle">
      <option name="closed" value="true" />
      <created>1753943249854</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1753943249854</updated>
    </task>
    <task id="LOCAL-00017" summary="切换influxdb">
      <option name="closed" value="true" />
      <created>1754015842459</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1754015842459</updated>
    </task>
    <task id="LOCAL-00018" summary="切换influxdb">
      <option name="closed" value="true" />
      <created>1754016373740</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1754016373740</updated>
    </task>
    <option name="localTasksCounter" value="19" />
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="RECENT_FILTERS">
      <map>
        <entry key="Branch">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="origin/liutong" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="origin/guazhou" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="维修总览功能相关代码新增" />
    <MESSAGE value="事件上报功能" />
    <MESSAGE value="维修总览和事件总览查询时间bug修复" />
    <MESSAGE value="设备权限功能修改，设备来源于实际物联网设备" />
    <MESSAGE value="用户新增控制密钥字段，增加相关功能，同时新增用户时自动添加密钥" />
    <MESSAGE value="生产控制，设备远程控制功能" />
    <MESSAGE value="删除设备等内容接口" />
    <MESSAGE value="修改包名" />
    <MESSAGE value="修改pom去除gradle内容，新增dockerfile" />
    <MESSAGE value="增加Californium文件" />
    <MESSAGE value="7.30" />
    <MESSAGE value="修改部分文件报名" />
    <MESSAGE value="修改netty-mqtt pom" />
    <MESSAGE value="修改maven-compiler-plugin 相关内容" />
    <MESSAGE value="修改pom不使用公共仓库" />
    <MESSAGE value="修改pom不使用公共仓库和gradle" />
    <MESSAGE value="切换influxdb" />
    <MESSAGE value="8.1" />
    <option name="LAST_COMMIT_MESSAGE" value="8.1" />
  </component>
</project>