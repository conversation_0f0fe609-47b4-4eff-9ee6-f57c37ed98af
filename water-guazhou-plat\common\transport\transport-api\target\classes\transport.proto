/**
 * Copyright © 2016-2019 The Thingsboard Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
syntax = "proto3";
package transport;

option java_package = "org.thingsboard.server.gen.transport";
option java_outer_classname = "TransportProtos";

/**
 * Data Structures;
 */
message SessionInfoProto {
  string nodeId = 1;
  int64 sessionIdMSB = 2;
  int64 sessionIdLSB = 3;
  int64 tenantIdMSB = 4;
  int64 tenantIdLSB = 5;
  int64 deviceIdMSB = 6;
  int64 deviceIdLSB = 7;
}

enum SessionEvent {
  OPEN = 0;
  CLOSED = 1;
}

enum SessionType {
  SYNC = 0;
  ASYNC = 1;
}

enum KeyValueType {
  BOOLEAN_V = 0;
  LONG_V = 1;
  DOUBLE_V = 2;
  STRING_V = 3;
}

message KeyValueProto {
  string key = 1;
  KeyValueType type = 2;
  bool bool_v = 3;
  int64 long_v = 4;
  double double_v = 5;
  string string_v = 6;
}

message TsKvProto {
  int64 ts = 1;
  KeyValueProto kv = 2;
}

message TsKvListProto {
  int64 ts = 1;
  repeated KeyValueProto kv = 2;
}

message DeviceInfoProto {
  int64 tenantIdMSB = 1;
  int64 tenantIdLSB = 2;
  int64 deviceIdMSB = 3;
  int64 deviceIdLSB = 4;
  string deviceName = 5;
  string deviceType = 6;
  string additionalInfo = 7;
}

/**
 * Messages that use Data Structures;
 */
message SessionEventMsg {
  SessionType sessionType = 1;
  SessionEvent event = 2;
}

message PostTelemetryMsg {
  repeated TsKvListProto tsKvList = 1;
}

message PostAttributeMsg {
  repeated KeyValueProto kv = 1;
}

message GetAttributeRequestMsg {
  int32 requestId = 1;
  repeated string clientAttributeNames = 2;
  repeated string sharedAttributeNames = 3;
}

message GetAttributeResponseMsg {
  int32 requestId = 1;
  repeated TsKvProto clientAttributeList = 2;
  repeated TsKvProto sharedAttributeList = 3;
  repeated string deletedAttributeKeys = 4;
  string error = 5;
}

message AttributeUpdateNotificationMsg {
  repeated TsKvProto sharedUpdated = 1;
  repeated string sharedDeleted = 2;
}

message ValidateDeviceTokenRequestMsg {
  string token = 1;
}

message ValidateDeviceX509CertRequestMsg {
  string hash = 1;
}

message ValidateDeviceCredentialsResponseMsg {
  DeviceInfoProto deviceInfo = 1;
  string credentialsBody = 2;
}

message GetOrCreateDeviceFromGatewayRequestMsg {
  int64 gatewayIdMSB = 1;
  int64 gatewayIdLSB = 2;
  string deviceName = 3;
  string deviceType = 4;
}

message GetOrCreateDeviceFromGatewayResponseMsg {
  DeviceInfoProto deviceInfo = 1;
}

message SessionCloseNotificationProto {
  string message = 1;
}

message SubscribeToAttributeUpdatesMsg {
  bool unsubscribe = 1;
}

message SubscribeToRPCMsg {
  bool unsubscribe = 1;
}

message ToDeviceRpcRequestMsg {
  int32 requestId = 1;
  string methodName = 2;
  string params = 3;
}

message ToDeviceRpcResponseMsg {
  int32 requestId = 1;
  string payload = 2;
}

message ToServerRpcRequestMsg {
  int32 requestId = 1;
  string methodName = 2;
  string params = 3;
}

message ToServerRpcResponseMsg {
  int32 requestId = 1;
  string payload = 2;
  string error = 3;
}

//Used to report session state to tb-node and persist this state in the cache on the tb-node level.
message SubscriptionInfoProto {
  int64 lastActivityTime = 1;
  bool attributeSubscription = 2;
  bool rpcSubscription = 3;
}

message SessionSubscriptionInfoProto {
  SessionInfoProto sessionInfo = 1;
  SubscriptionInfoProto subscriptionInfo = 2;
}

message DeviceSessionsCacheEntry {
  repeated SessionSubscriptionInfoProto sessions = 1;
}

message TransportToDeviceActorMsg {
  SessionInfoProto sessionInfo = 1;
  SessionEventMsg sessionEvent = 2;
  PostTelemetryMsg postTelemetry = 3;
  PostAttributeMsg postAttributes = 4;
  GetAttributeRequestMsg getAttributes = 5;
  SubscribeToAttributeUpdatesMsg subscribeToAttributes = 6;
  SubscribeToRPCMsg subscribeToRPC = 7;
  ToDeviceRpcResponseMsg toDeviceRPCCallResponse = 8;
  ToServerRpcRequestMsg toServerRPCCallRequest = 9;
  SubscriptionInfoProto subscriptionInfo = 10;
}

message DeviceActorToTransportMsg {
   int64 sessionIdMSB = 1;
   int64 sessionIdLSB = 2;
   SessionCloseNotificationProto sessionCloseNotification = 3;
   GetAttributeResponseMsg getAttributesResponse = 4;
   AttributeUpdateNotificationMsg attributeUpdateNotification = 5;
   ToDeviceRpcRequestMsg toDeviceRequest = 6;
   ToServerRpcResponseMsg toServerResponse = 7;
}

/**
 * Main messages;
 */
message ToRuleEngineMsg {
  TransportToDeviceActorMsg toDeviceActorMsg = 1;
}

message ToTransportMsg {
  DeviceActorToTransportMsg toDeviceSessionMsg = 1;
}

message TransportApiRequestMsg {
   ValidateDeviceTokenRequestMsg validateTokenRequestMsg = 1;
   ValidateDeviceX509CertRequestMsg validateX509CertRequestMsg = 2;
   GetOrCreateDeviceFromGatewayRequestMsg getOrCreateDeviceRequestMsg = 3;
}

message TransportApiResponseMsg {
   ValidateDeviceCredentialsResponseMsg validateTokenResponseMsg = 1;
   GetOrCreateDeviceFromGatewayResponseMsg getOrCreateDeviceResponseMsg = 2;
}
