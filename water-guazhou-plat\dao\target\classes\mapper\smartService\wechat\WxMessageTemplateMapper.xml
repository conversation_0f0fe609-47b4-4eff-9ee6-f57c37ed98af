<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartService.wechat.WxMessageTemplateMapper">
    <sql id="Base_Column_List">
        <!--@sql select -->id,
                           name,
                           template_id,
                           template,
                           create_time,
                           tenant_id<!--@sql from wx_message_template -->
    </sql>
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageTemplate">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="template_id" property="templateId"/>
        <result column="template" property="template"/>
        <result column="create_time" property="createTime"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>
    <select id="findByPage" resultType="org.thingsboard.server.dao.model.sql.smartService.wechat.WxMessageTemplate">
        select
        <include refid="Base_Column_List"/>
        from wx_message_template
        <where>
            and tenant_id = #{tenantId}
        </where>
        order by create_time
    </select>

    <update id="update">
        update wx_message_template
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="templateId != null">
                template_id = #{templateId},
            </if>
            <if test="template != null">
                template = #{template},
            </if>
        </set>
        where id = #{id}
    </update>

    <select id="getWechatTemplateId" resultType="java.lang.String">
        select template_id
        from wx_message_template
        where id = #{id}
    </select>
</mapper>