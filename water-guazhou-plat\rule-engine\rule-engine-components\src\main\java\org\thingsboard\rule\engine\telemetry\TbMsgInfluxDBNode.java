/**
 * Copyright © 2016-2019 The Thingsboard Authors
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.thingsboard.rule.engine.telemetry;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.thingsboard.rule.engine.api.*;
import org.thingsboard.server.common.data.DataConstants;
import org.thingsboard.server.common.data.Device;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.id.DeviceId;
import org.thingsboard.server.common.data.kv.BasicTsKvEntry;
import org.thingsboard.server.common.data.kv.DoubleDataEntry;
import org.thingsboard.server.common.data.kv.TsKvEntry;
import org.thingsboard.server.common.data.plugin.ComponentType;
import org.thingsboard.server.common.data.tsdb.DataPoint;
import org.thingsboard.server.common.data.utils.CharUtil;
import org.thingsboard.server.common.data.utils.StringUtils;
import org.thingsboard.server.common.msg.TbMsg;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 保存遥测数据到时序数据库节点
 * 基于可配置的TTL参数保存时间序列遥测数据。 使用“POST_TELEMETRY_REQUEST”消息类型预期消息
 */
@Slf4j
@RuleNode(
        type = ComponentType.ACTION,
        name = "持久化: InfluxDB",
        configClazz = TbMsgTimeseriesNodeConfiguration.class,
        nodeDescription = "将时间序列数据保存到 influxDB",
        nodeDetails = "根据可配置的 TTL 参数保存时间序列遥测数据。期望具有“POST_TELEMETRY_REQUEST”消息类型的消息",
        uiResources = {"static/rulenode/rulenode-core-config.js", "static/rulenode/rulenode-core-config.css"},
        configDirective = "tbActionNodeTimeseriesConfig",
        icon = "file_upload"
)
public class TbMsgInfluxDBNode implements TbNode {

    @Override
    public void init(TbContext ctx, TbNodeConfiguration configuration) throws TbNodeException {
    }

    /**
     * 组装数据然后保存到时序数据库
     *
     * @param ctx
     * @param msg
     */
    @Override
    public void onMsg(TbContext ctx, TbMsg msg) {

        List<DataPoint> dataPoints = new ArrayList<>();

        if (msg.getMetaData().getTsKvEntryList() == null || msg.getMetaData().getTsKvEntryList().size() < 1) {
            ctx.tellNext(msg, TbRelationTypes.FAILURE);
            return;
        }
        Device device = ctx.getDeviceService().findDeviceById(new DeviceId(UUIDConverter.fromString(msg.getMetaData().getValue(DataConstants.DEVICE_ID))));
        boolean isDataSource = device.getType().equalsIgnoreCase(DataConstants.PROTOCOL_TYPE_MODBUS) && device.getGateWayId() == null;
        //工控机上传数据源数据，直接返回
        if (isDataSource) {
            msg.getMetaData().getTsKvEntryList().forEach(tsKvEntry -> {
                dataPoints.add(new DataPoint(tsKvEntry.getKey().split("\\.")[0], tsKvEntry.getTs(), tsKvEntry.getValueAsString(), tsKvEntry.getKey().split("\\.")[1]));
            });
        } else {
            msg.getMetaData().getTsKvEntryList().forEach(key -> {
                //先判断是否为空数据，如果为空数据则直接过滤掉
                if (!StringUtils.checkNotNull(key.getValueAsString())) {
                    return;
                }
                HashMap<String, String> hashMap = new HashMap<>();
                // 当前tag中是否包含中文
                if (CharUtil.isChinese(key.getKey())) {
                    try {
                        String tagBase64 = new String(Base64.encodeBase64(key.getKey().getBytes()))
                                .replace("+", "")
                                .replace("=", "")
                                .replace("/", "");

                        hashMap.put("prop", tagBase64);
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("tag中文转换失败");
                        hashMap.put("prop", key.getKey());
                    }
                } else {
                    hashMap.put("prop", key.getKey());
                }
                dataPoints.add(new DataPoint(msg.getMetaData().getValue("deviceId"), key.getTs(), key.getValueAsString(), hashMap.get("prop"))
                );
            });
        }
        if (dataPoints.size() > 0) {
//            save2TSDBByPower(ctx, msg, dataPoints, msg.getMetaData().getTsKvEntryList(), new DeviceId(UUIDConverter.fromString(msg.getMetaData().getValue("deviceId"))), isDataSource);
        } else {
            ctx.tellNext(msg, TbRelationTypes.FAILURE);
        }
    }

    /**
     * 使用实例代码进行提交
     *
     * @param ctx
     * @param msg
     * @param dataPoints
     * @param ts
     */
    public void save2TSDBByPower(TbContext ctx, TbMsg msg, List<DataPoint> dataPoints, List<TsKvEntry> ts, DeviceId deviceId, boolean isDataSource) {
        long start = System.currentTimeMillis();
        ctx.getInfluxService().saveDeviceToInflux(dataPoints);
        ctx.getDeviceAuthService().updateDeviceLastOnline(deviceId);
        try {
            log.info(msg.getMetaData().getValue("deviceName") + ", data time = [" + dataPoints.get(0).getTimestamp() + "], push数据到Influx耗时+" + (System.currentTimeMillis() - start));
        } catch (Exception ignored) {
        }
        if (isDataSource) {
            ts.forEach(tsKvEntry -> {
                TsKvEntry tsKvEntry1 = new BasicTsKvEntry(tsKvEntry.getTs(), new DoubleDataEntry(tsKvEntry.getKey().split("\\.")[1], Double.parseDouble(tsKvEntry.getValueAsString())));
                ctx.getTimeseriesService().save(new DeviceId(UUIDConverter.fromString(tsKvEntry.getKey().split("\\.")[0])), tsKvEntry1);
            });
        } else {
            ts.forEach(tsKvEntry -> ctx.getTimeseriesService().save(deviceId, tsKvEntry));
        }
        ctx.tellNext(msg, TbRelationTypes.SUCCESS);
    }

    @Override
    public void destroy() {
    }

    /**
     * TODO 紧急补丁  --预防OPENTSDB挂掉情况下丢失数据，故当上传数据失败时，对数据进行保存，并通过邮件和短信通知开发人员
     */
    private void sendDataFailure(TbContext ctx, TbMsg msg, String result) {
        //保存数据到数据库
        ctx.getTelemetryService().saveAndNotify(ctx.getTenantId(), msg.getOriginator(), msg.getMetaData().getTsKvEntryList());
//        //发送短信和邮件给开发人员
//        try {
//            ctx.getMailService().sendEmail(DataConstants.JERRY_EMAIL, "系统出错报告", "系统出现异常，数据无法正常保存，尽快前往查看！+msg :" + JacksonUtil.toString(msg) + ";,数据保存失败原因" + result);
//            //ctx.getMailService().sendSMS(DataConstants.JERRY_PHONE, "系统异常");
//        } catch (ThingsboardException e) {
//            e.printStackTrace();
//        }

    }
}
